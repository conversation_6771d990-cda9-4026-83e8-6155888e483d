// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

interface IDex {
    function swap(address tokenIn, address tokenOut, uint amountIn) external returns (uint amountOut);
}

contract Arbitrage {
    address public owner;

    constructor() {
        owner = msg.sender;
    }

    function executeSwap(
        address dexBuy,
        address dexSell,
        address tokenIn,
        address tokenOut,
        uint amountIn
    ) external {
        require(msg.sender == owner, "Not authorized");

        uint amountOutBuy = IDex(dexBuy).swap(tokenIn, tokenOut, amountIn);
        uint amountOutSell = IDex(dexSell).swap(tokenOut, tokenIn, amountOutBuy);

        require(amountOutSell > amountIn, "No profit");
    }
function getOwner() public view returns (address) {
    return owner;
}
}

