// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;
import "hardhat/console.sol";

contract MockDexRouter {
    function swapExactTokensForTokens(
    uint /* amountIn */,
    uint /* amountOutMin */,
    address[] calldata /* path */,
    address /* to */,
    uint /*deadline*/
) external pure returns (uint[] memory amounts) {
    // Return dummy values for testing
    amounts = new uint[](2);
    amounts[0] = 1000;
    amounts[1] = 950;
}

}
