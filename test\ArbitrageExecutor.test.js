const { expect } = require("chai");

describe("SwapCrypto", function () {
  let tokenA, tokenB, router, swapContract, owner;

  beforeEach(async function () {
    const [deployer] = await ethers.getSigners();
    owner = deployer;

    const MockERC20 = await ethers.getContractFactory("MockERC20");
    tokenA = await MockERC20.deploy("TokenA", "TKA", 18);
    tokenB = await MockERC20.deploy("TokenB", "TKB", 18);

    const MockDexRouter = await ethers.getContractFactory("MockDexRouter");
    router = await MockDexRouter.deploy();

    const SwapCrypto = await ethers.getContractFactory("SwapCrypto");
    swapContract = await SwapCrypto.deploy(router.address);

    // Mint tokens to owner
    await tokenA.mint(owner.address, ethers.utils.parseEther("1000"));
    await tokenB.mint(owner.address, ethers.utils.parseEther("1000"));

    // Approve router to spend tokenA
    await tokenA.approve(router.address, ethers.utils.parseEther("1000"));
  });

  it("should simulate a token swap", async function () {
    const path = [tokenA.address, tokenB.address];
    const amountIn = ethers.utils.parseEther("100");
    const amountOutMin = ethers.utils.parseEther("90");

    const tx = await swapContract.swapTokens(
      amountIn,
      amountOutMin,
      path,
      owner.address
    );

    await tx.wait();

    // Since it's a mock, we just assert the transaction didn't revert
    expect(tx).to.not.be.reverted;
  });
});
