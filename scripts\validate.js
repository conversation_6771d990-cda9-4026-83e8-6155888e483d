const { ethers } = require("hardhat");

async function main() {
  const deployedAddress = "******************************************"; // your deployed contract
  const arbitrage = await ethers.getContractAt("Arbitrage", deployedAddress);

  // Example: call a public view function
const owner = await arbitrage.getOwner();
console.log("👑 Contract owner:", owner);

}

main().catch((error) => {
  console.error("❌ Error:", error);
  process.exit(1);
});
