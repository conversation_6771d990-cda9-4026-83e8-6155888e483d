import hardhat from "hardhat";

const { ethers } = hardhat;

async function main() {
  const initialSupply = ethers.utils.parseUnits("1000000", 18);

  const TokenA = await ethers.deployContract("MockERC20", [
    "TokenA",
    "TKA",
    initialSupply,
  ]);
  await TokenA.waitForDeployment();
  console.log("🟢 TokenA deployed at:", await TokenA.getAddress());

  const TokenB = await ethers.deployContract("MockERC20", [
    "TokenB",
    "TKB",
    initialSupply,
  ]);
  await TokenB.waitForDeployment();
  console.log("🔵 TokenB deployed at:", await TokenB.getAddress());
}

main().catch((err) => {
  console.error("❌ Token deployment failed:", err);
  process.exit(1);
});
