// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

import "./IDex.sol"; // or redefine the interface here if needed

contract MockDex is IDex {
    uint public rate; // simple multiplier for swap simulation

    constructor(uint _rate) {
        rate = _rate;
    }

    function swap(address /*tokenIn*/, address /*tokenOut*/, uint amountIn) external view override returns (uint amountOut) {
        // Simulate a swap with a fixed rate
        amountOut = amountIn * rate;
    }
}
